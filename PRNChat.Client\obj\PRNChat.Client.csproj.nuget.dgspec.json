{"format": 1, "restore": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Client\\PRNChat.Client.csproj": {}}, "projects": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Client\\PRNChat.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Client\\PRNChat.Client.csproj", "projectName": "PRNChat.Client", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Client\\PRNChat.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Shared\\PRNChat.Shared.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Shared\\PRNChat.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Shared\\PRNChat.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Shared\\PRNChat.Shared.csproj", "projectName": "PRNChat.Shared", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Shared\\PRNChat.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRN-Chat-main\\PRNChat.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}
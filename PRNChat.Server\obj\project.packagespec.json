﻿"restore":{"projectUniqueName":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRNChat\\PRNChat.Server\\PRNChat.Server.csproj","projectName":"PRNChat.Server","projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRNChat\\PRNChat.Server\\PRNChat.Server.csproj","outputPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRNChat\\PRNChat.Server\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRNChat\\PRNChat.Shared\\PRNChat.Shared.csproj":{"projectPath":"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\TeamProject\\PRNChat\\PRNChat.Shared\\PRNChat.Shared.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[8.0.17, )"},"Supabase":{"target":"Package","version":"[1.1.1, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[6.6.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}